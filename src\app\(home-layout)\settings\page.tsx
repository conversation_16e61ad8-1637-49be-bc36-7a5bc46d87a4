"use client";

import { ProfilePicture } from "@/components/settings/profile-picture";

export default () => {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Settings</h1>
        <p className="text-muted-foreground">Manage your account settings and preferences.</p>
      </div>

      <div className="grid gap-6">
        <div className="space-y-4">
          <div>
            <h2 className="text-xl font-semibold">Public Profile Picture</h2>
            <p className="text-sm text-muted-foreground">This profile picture uses public URLs (no signing required)</p>
          </div>
          <ProfilePicture isPrivate={false} />
        </div>

        <div className="space-y-4">
          <div>
            <h2 className="text-xl font-semibold">Private Profile Picture</h2>
            <p className="text-sm text-muted-foreground">This profile picture uses signed URLs for secure access</p>
          </div>
          <ProfilePicture isPrivate={true} />
        </div>
      </div>
    </div>
  );
};
