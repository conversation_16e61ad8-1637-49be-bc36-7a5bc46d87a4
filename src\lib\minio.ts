import { Client } from "minio";

const EXPIRE = 60 * 60 * 24; // 24 hours

export const minio = new Client({
  endPoint: process.env.S3_ENDPOINT || "localhost",
  port: Number.parseInt(process.env.S3_PORT as string, 10),
  useSSL: false,
  accessKey: process.env.S3_ROOT_USER as string,
  secretKey: process.env.S3_ROOT_PASSWORD as string,
});

export const createBucket = async (bucketName: string, isPrivate: boolean = false): Promise<void> => {
  if (!(await minio.bucketExists(bucketName))) {
    await minio.makeBucket(bucketName, "eu-east-01");

    if (!isPrivate) {
      const policy = {
        Version: "2012-10-17",
        Statement: [
          {
            Effect: "Allow",
            Principal: { AWS: ["*"] },
            Action: ["s3:GetObject"],
            Resource: [`arn:aws:s3:::${bucketName}/*`],
          },
        ],
      };
      await minio.setBucketPolicy(bucketName, JSON.stringify(policy));
    }
  }
};

export const getPublicAvatarUrl = (object: string, isPrivate: boolean = false): string | null => {
  if (!object) return null;
  const bucket = isPrivate ? "avatars-private" : "avatars-public";
  const endpoint = process.env.S3_ENDPOINT || "localhost";
  const port = process.env.S3_PORT;
  const baseUrl = `http://${endpoint}${port ? `:${port}` : ""}`;
  return `${baseUrl}/${bucket}/${object}`;
};

export const uploadProfilePicture = async (
  userId: string,
  file: Buffer,
  fileName: string,
  contentType: string,
  isPrivate: boolean = false
): Promise<string | null> => {
  const bucket = isPrivate ? "avatars-private" : "avatars-public";
  const objectName = `${userId}/${fileName}`;
  try {
    await createBucket(bucket, isPrivate);
    await minio.putObject(bucket, objectName, file, file.length, {
      "Content-Type": contentType,
    });
    return objectName;
  } catch (err) {
    console.error("Error uploading profile picture:", err);
    return null;
  }
};

export const deleteProfilePicture = async (objectName: string, isPrivate: boolean = false): Promise<boolean> => {
  const bucket = isPrivate ? "avatars-private" : "avatars-public";
  try {
    await minio.removeObject(bucket, objectName);
    return true;
  } catch (err) {
    console.error("Error deleting profile picture:", err);
    return false;
  }
};

export const getProfilePictureUrl = (objectName?: string, isPrivate: boolean = false): string | null => {
  if (!objectName) return null;
  return getPublicAvatarUrl(objectName, isPrivate);
};

export const getSignedProfilePictureUrl = async (objectName?: string, isPrivate: boolean = false): Promise<string | null> => {
  if (!objectName) return null;
  const bucket = isPrivate ? "avatars-private" : "avatars-public";
  try {
    await createBucket(bucket, isPrivate);
    return await minio.presignedGetObject(bucket, objectName, EXPIRE);
  } catch (err) {
    console.error("Error generating signed URL:", err);
    return null;
  }
};
