"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { authClient } from "@/lib/auth-client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/toast";

export default () => {
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [loading, setLoading] = useState(false);

  const router = useRouter();
  return (
    <div className="min-h-screen flex items-center justify-center bg-muted/40 p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center">{isLogin ? "Login to your account" : "Create your account"}</CardTitle>
        </CardHeader>
        <CardContent>
          <form
            onSubmit={async (e: React.FormEvent) => {
              e.preventDefault();
              setLoading(true);
              try {
                if (isLogin) {
                  const temp = await authClient.signIn.email({ email, password });
                  if (temp.error || !temp.data) throw temp.error;
                } else {
                  const temp = await authClient.signUp.email({ email, password, name });
                  if (temp.error || !temp.data) throw temp.error;
                }
                router.push("/dashboard");
              } catch (err: any) {
                toast.error(err?.message || "Authentication failed");
              } finally {
                setLoading(false);
              }
            }}
            className="space-y-4">
            {!isLogin && (
              <div className="space-y-1">
                <Label htmlFor="name">Full Name</Label>
                <Input id="name" type="text" value={name} onChange={(e) => setName(e.target.value)} placeholder="Full name" required />
              </div>
            )}

            <div className="space-y-1">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Email address"
                required
              />
            </div>

            <div className="space-y-1">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Password"
                required
              />
            </div>

            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Loading..." : isLogin ? "Sign In" : "Sign Up"}
            </Button>

            <Button type="button" variant="link" className="w-full text-sm" onClick={() => setIsLogin(!isLogin)}>
              {isLogin ? "Need an account? Sign up" : "Already have an account? Sign in"}
            </Button>
          </form>

          <Separator className="mb-9">
            <div className="text-center pt-2">Or continue with</div>
          </Separator>

          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="outline"
              onClick={async () => {
                setLoading(true);
                try {
                  await authClient.signIn.social({
                    provider: "google",
                    callbackURL: "/dashboard",
                  });
                } catch (err: any) {
                  toast.error(err.message || "Google authentication failed");
                } finally {
                  setLoading(false);
                }
              }}
              disabled={loading}>
              Google
            </Button>

            <Button
              variant="outline"
              onClick={async () => {
                setLoading(true);
                try {
                  await authClient.signIn.social({
                    provider: "github",
                    callbackURL: "/dashboard",
                  });
                } catch (err: any) {
                  toast.error(err.message || "GitHub authentication failed");
                } finally {
                  setLoading(false);
                }
              }}
              disabled={loading}>
              GitHub
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
