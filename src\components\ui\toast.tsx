"use client";

import React from "react";
import { toast as sonnerToast } from "sonner";
import { cva } from "class-variance-authority";
import { cn } from "@/lib/utils"; // your typical tailwind merge helper

type ToastVariant = "default" | "success" | "error" | "warning" | "info";

const temp = (message: string, variant: ToastVariant = "default") =>
  sonnerToast.custom((id) => <Toast id={id} title={message} variant={variant} />);

export const toast = Object.assign(temp, {
  show: temp,
  success: (message: string) => temp(message, "success"),
  error: (message: string) => temp(message, "error"),
  warning: (message: string) => temp(message, "warning"),
  info: (message: string) => temp(message, "info"),
});

const toastVariants = cva("flex rounded-lg shadow-lg ring-1 ring-black/5 w-full md:max-w-[364px] items-center p-4", {
  variants: {
    variant: {
      default: "bg-background text-foreground",
      success: "bg-green-100 text-green-900 dark:bg-green-900 dark:text-green-100",
      error: "bg-red-100 text-red-900 dark:bg-red-900 dark:text-red-100",
      warning: "bg-yellow-100 text-yellow-900 dark:bg-yellow-900 dark:text-yellow-100",
      info: "bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100",
    },
  },
  defaultVariants: {
    variant: "default",
  },
});

const Toast = (props: { id: string | number; title: string; variant: ToastVariant }) => {
  const { title, variant } = props;

  return (
    <div className="relative retro">
      <div className={cn(toastVariants({ variant }))}>
        <div className="flex flex-1 items-center">
          <div className="w-full">
            <p className="text-sm font-medium">{title}</p>
          </div>
        </div>
      </div>
      <div className="absolute -top-1.5 w-1/2 left-1.5 h-1.5 bg-foreground dark:bg-ring" />
      <div className="absolute -top-1.5 w-1/2 right-1.5 h-1.5 bg-foreground dark:bg-ring" />
      <div className="absolute -bottom-1.5 w-1/2 left-1.5 h-1.5 bg-foreground dark:bg-ring" />
      <div className="absolute -bottom-1.5 w-1/2 right-1.5 h-1.5 bg-foreground dark:bg-ring" />
      <div className="absolute top-0 left-0 size-1.5 bg-foreground dark:bg-ring" />
      <div className="absolute top-0 right-0 size-1.5 bg-foreground dark:bg-ring" />
      <div className="absolute bottom-0 left-0 size-1.5 bg-foreground dark:bg-ring" />
      <div className="absolute bottom-0 right-0 size-1.5 bg-foreground dark:bg-ring" />
      <div className="absolute top-1 -left-1.5 h-1/2 w-1.5 bg-foreground dark:bg-ring" />
      <div className="absolute bottom-1 -left-1.5 h-1/2 w-1.5 bg-foreground dark:bg-ring" />
      <div className="absolute top-1 -right-1.5 h-1/2 w-1.5 bg-foreground dark:bg-ring" />
      <div className="absolute bottom-1 -right-1.5 h-1/2 w-1.5 bg-foreground dark:bg-ring" />
    </div>
  );
};
