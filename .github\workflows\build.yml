name: Build

on:
  pull_request:
  push:
    branches: ["main"]

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      DATABASE_HOST: localhost
      DATABASE_PORT: 5432
      DATABASE_USER: test
      DATABASE_PASSWORD: test
      DATABASE_DB: test
      REDIS_PORT: 6379
      S3_PORT: 9000
      S3_CONSOLE_PORT: 9001
      S3_ROOT_USER: minioadmin
      S3_ROOT_PASSWORD: minioadmin
      BETTER_AUTH_SECRET: test-secret-key-for-build
      BETTER_AUTH_URL: http://localhost:3000
      NEXT_PUBLIC_BETTER_AUTH_URL: http://localhost:3000
      GOOGLE_CLIENT_ID: test
      GOOGLE_CLIENT_SECRET: test
      GITHUB_CLIENT_ID: test
      GITHUB_CLIENT_SECRET: test
    steps:
      - uses: actions/checkout@v5
      - uses: actions/setup-node@v5
        with:
          cache: 'npm'

      - name: Cache Next.js build
        uses: actions/cache@v4
        with:
          path: |
            ~/.npm
            ${{ github.workspace }}/.next/cache
          key: ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-${{ hashFiles('**/*.js', '**/*.jsx', '**/*.ts', '**/*.tsx') }}
          restore-keys: |
            ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-

      - run: npm ci
      - name: Run build
        run: npm run build
