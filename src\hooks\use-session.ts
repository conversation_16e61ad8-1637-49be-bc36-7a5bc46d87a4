"use client";

import { useEffect, useState } from "react";
import { authClient } from "@/lib/auth-client";

type User = {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image?: string;
  createdAt: Date;
  updatedAt: Date;
};

type Session = {
  session: {
    id: string;
    expiresAt: Date;
    token: string;
    createdAt: Date;
    updatedAt: Date;
    ipAddress?: string;
    userAgent?: string;
    userId: string;
  };
  user: User;
};

export const useSession = () => {
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getSession = async () => {
      try {
        setLoading(true);
        const { data, error } = await authClient.getSession();
        
        if (error) {
          setError(error.message || "Failed to get session");
          setSession(null);
        } else {
          setSession(data);
          setError(null);
        }
      } catch (err: any) {
        setError(err.message || "Failed to get session");
        setSession(null);
      } finally {
        setLoading(false);
      }
    };

    getSession();
  }, []);

  const refreshSession = async () => {
    try {
      setLoading(true);
      const { data, error } = await authClient.getSession();
      
      if (error) {
        setError(error.message || "Failed to refresh session");
        setSession(null);
      } else {
        setSession(data);
        setError(null);
      }
    } catch (err: any) {
      setError(err.message || "Failed to refresh session");
      setSession(null);
    } finally {
      setLoading(false);
    }
  };

  return {
    session,
    user: session?.user || null,
    loading,
    error,
    refreshSession,
  };
};
